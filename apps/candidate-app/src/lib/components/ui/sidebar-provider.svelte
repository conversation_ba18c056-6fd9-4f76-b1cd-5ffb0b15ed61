<script lang="ts">
  import { setContext, onMount } from 'svelte'
  import { createSidebarContext } from './sidebar.svelte'
  import { cn } from '$lib/utils'
  
  let className: string = ''
  export { className as class }
  export let defaultOpen = true
  export let open: boolean | undefined = undefined
  export let onOpenChange: ((open: boolean) => void) | undefined = undefined
  
  const context = createSidebarContext(defaultOpen)
  
  // Set up controlled state if provided
  if (open !== undefined && onOpenChange) {
    context.setOpen = (value: boolean) => {
      onOpenChange(value)
    }
  }
  
  setContext('sidebar', context)
  
  onMount(() => {
    // Read cookie state on mount
    if (typeof document !== 'undefined' && open === undefined) {
      const cookieValue = document.cookie
        .split('; ')
        .find(row => row.startsWith('sidebar_state='))
        ?.split('=')[1]
      
      if (cookieValue) {
        context.setOpen(cookieValue === 'true')
      }
    }
  })
</script>

<div
  class={cn(
    'flex min-h-screen w-full',
    className
  )}
  style="--sidebar-width: 16rem; --sidebar-width-mobile: 18rem;"
  {...$$restProps}
>
  <slot />
</div>
