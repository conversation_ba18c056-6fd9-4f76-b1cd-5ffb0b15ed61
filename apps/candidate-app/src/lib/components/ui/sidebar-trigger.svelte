<script lang="ts">
  import { cn } from '$lib/utils'
  import { useSidebar } from './sidebar.svelte'
  import { Menu } from 'lucide-svelte'
  
  let className: string = ''
  export { className as class }
  
  const { toggleSidebar } = useSidebar()
</script>

<button
  class={cn(
    'inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors hover:bg-accent hover:text-accent-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',
    'h-7 w-7',
    className
  )}
  on:click={toggleSidebar}
  {...$$restProps}
>
  <Menu class="h-4 w-4" />
  <span class="sr-only">Toggle Sidebar</span>
</button>
