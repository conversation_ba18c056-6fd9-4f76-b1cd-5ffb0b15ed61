<script lang="ts" context="module">
  import { writable } from 'svelte/store'
  import { getContext } from 'svelte'
  
  const SIDEBAR_COOKIE_NAME = "sidebar_state"
  const SIDEBAR_COOKIE_MAX_AGE = 60 * 60 * 24 * 365 // 1 year
  const SIDEBAR_WIDTH = "16rem"
  const SIDEBAR_WIDTH_MOBILE = "18rem"
  const SIDEBAR_KEYBOARD_SHORTCUT = "b"

  interface SidebarContext {
    state: 'expanded' | 'collapsed'
    open: boolean
    setOpen: (open: boolean) => void
    openMobile: boolean
    setOpenMobile: (open: boolean) => void
    isMobile: boolean
    toggleSidebar: () => void
  }

  const SIDEBAR_CONTEXT_KEY = 'sidebar'

  export function createSidebarContext(defaultOpen = true) {
    const open = writable(defaultOpen)
    const openMobile = writable(false)
    const isMobile = writable(false)
    
    const context: SidebarContext = {
      get state(): 'expanded' | 'collapsed' {
        return 'expanded' // For now, we'll implement collapsible later
      },
      get open() {
        let value = false
        open.subscribe(v => value = v)()
        return value
      },
      setOpen: (value: boolean) => {
        open.set(value)
        // Set cookie to persist state
        if (typeof document !== 'undefined') {
          document.cookie = `${SIDEBAR_COOKIE_NAME}=${value}; path=/; max-age=${SIDEBAR_COOKIE_MAX_AGE}`
        }
      },
      get openMobile() {
        let value = false
        openMobile.subscribe(v => value = v)()
        return value
      },
      setOpenMobile: (value: boolean) => {
        openMobile.set(value)
      },
      get isMobile() {
        let value = false
        isMobile.subscribe(v => value = v)()
        return value
      },
      toggleSidebar: () => {
        const currentMobile = context.isMobile
        if (currentMobile) {
          context.setOpenMobile(!context.openMobile)
        } else {
          context.setOpen(!context.open)
        }
      }
    }
    
    return context
  }

  export function useSidebar(): SidebarContext {
    const context = getContext<SidebarContext>(SIDEBAR_CONTEXT_KEY)
    if (!context) {
      throw new Error("useSidebar must be used within a SidebarProvider.")
    }
    return context
  }
</script>

<script lang="ts">
  import { cn } from '$lib/utils'
  import { onMount } from 'svelte'
  
  let className: string = ''
  export { className as class }
  export let side: 'left' | 'right' = 'left'
  export let variant: 'sidebar' | 'floating' | 'inset' = 'sidebar'
  export let collapsible: 'offcanvas' | 'icon' | 'none' = 'offcanvas'

  let context: SidebarContext | null = null

  try {
    context = useSidebar()
  } catch (e) {
    // Sidebar not in provider context, that's ok
  }
  
  onMount(() => {
    if (!context) return

    // Handle keyboard shortcut
    const handleKeydown = (e: KeyboardEvent) => {
      if (e.key === SIDEBAR_KEYBOARD_SHORTCUT && (e.metaKey || e.ctrlKey)) {
        e.preventDefault()
        context?.toggleSidebar()
      }
    }

    // Check if mobile
    const checkMobile = () => {
      context?.setOpenMobile(window.innerWidth < 1024)
    }

    window.addEventListener('keydown', handleKeydown)
    window.addEventListener('resize', checkMobile)
    checkMobile()

    return () => {
      window.removeEventListener('keydown', handleKeydown)
      window.removeEventListener('resize', checkMobile)
    }
  })
</script>

<aside
  class={cn(
    'relative h-screen w-64 bg-sidebar text-sidebar-foreground',
    'flex flex-col border-r border-sidebar-border',
    variant === 'floating' && 'rounded-lg border shadow-lg',
    variant === 'inset' && 'm-2 rounded-lg border shadow-sm',
    side === 'right' && 'border-l border-r-0',
    className
  )}
  style="--sidebar-width: {SIDEBAR_WIDTH}; --sidebar-width-mobile: {SIDEBAR_WIDTH_MOBILE};"
  {...$$restProps}
>
  <slot />
</aside>
