<script lang="ts">
  import { page } from '$app/stores'
  import { Bell } from 'lucide-svelte'
  import { onMount } from 'svelte'
  
  import { SidebarProvider, SidebarTrigger } from '$lib/components/ui/sidebar'
  import CandidateSidebar from './CandidateSidebar.svelte'
  
  export let user: any = null
  export let candidateProfile: any = null
  
  let isMobile = false
  
  onMount(() => {
    const checkMobile = () => {
      isMobile = window.innerWidth < 1024
    }
    
    checkMobile()
    window.addEventListener('resize', checkMobile)
    
    return () => {
      window.removeEventListener('resize', checkMobile)
    }
  })
  
  // Generate page title from route
  $: pageTitle = generatePageTitle($page.url.pathname)
  
  function generatePageTitle(pathname: string): string {
    const segments = pathname.split('/').filter(Boolean)
    if (segments.length === 0) return 'Dashboard'
    
    const titleMap: Record<string, string> = {
      'dashboard': 'Dashboard',
      'jobs': 'Job Search',
      'applications': 'My Applications',
      'profile': 'My Profile',
      'settings': 'Settings'
    }
    
    return titleMap[segments[segments.length - 1]] || segments[segments.length - 1]
  }
</script>

<SidebarProvider defaultOpen={!isMobile}>
  <div class="flex min-h-screen w-full bg-background">
    <!-- Sidebar -->
    <CandidateSidebar {user} {candidateProfile} />
    
    <!-- Main Content -->
    <div class="flex flex-1 flex-col overflow-hidden">
      <!-- Top Header -->
      <header class="flex h-14 items-center gap-4 border-b bg-background px-4 lg:px-6">
        <SidebarTrigger class="lg:hidden" />
        
        <div class="flex-1">
          <h1 class="text-lg font-semibold text-foreground lg:text-xl">{pageTitle}</h1>
        </div>
        
        <!-- Header actions -->
        <div class="flex items-center gap-3">
          <button 
            class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors hover:bg-accent hover:text-accent-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 h-9 w-9" 
            title="Notifications"
          >
            <Bell class="h-4 w-4" />
            <span class="sr-only">Notifications</span>
          </button>
        </div>
      </header>
      
      <!-- Main content area -->
      <main class="flex-1 overflow-auto p-4 lg:p-6">
        <slot />
      </main>
    </div>
  </div>
</SidebarProvider>
