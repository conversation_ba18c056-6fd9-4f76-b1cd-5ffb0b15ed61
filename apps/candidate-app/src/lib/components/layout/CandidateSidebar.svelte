<script lang="ts">
  import { page } from '$app/stores'
  import { goto } from '$app/navigation'
  import { 
    Home, 
    Search, 
    FileText, 
    User,
    Settings,
    UserCheck,
    LogOut
  } from 'lucide-svelte'
  
  import {
    Sidebar,
    SidebarContent,
    SidebarFooter,
    SidebarGroup,
    SidebarGroupContent,
    SidebarGroupLabel,
    SidebarHeader,
    SidebarMenu,
    SidebarMenuButton,
    SidebarMenuItem
  } from '$lib/components/ui/sidebar'
  
  export let user: any = null
  export let candidateProfile: any = null
  
  // Navigation items for candidates
  const navigationItems = [
    {
      section: 'Main',
      items: [
        { name: 'Dashboard', href: '/dashboard', icon: Home },
        { name: 'Job Search', href: '/jobs', icon: Search },
        { name: 'Applications', href: '/applications', icon: FileText },
      ]
    },
    {
      section: 'Profile',
      items: [
        { name: 'My Profile', href: '/profile', icon: User },
        { name: 'Setting<PERSON>', href: '/settings', icon: Settings },
      ]
    }
  ]
  
  function navigateTo(href: string) {
    goto(href)
  }
  
  async function signOut() {
    const response = await fetch('/api/auth/signout', { method: 'POST' })
    if (response.ok) {
      goto('/')
    }
  }
  
  // Check if current path is active
  function isActive(href: string): boolean {
    if (href === '/dashboard') {
      return $page.url.pathname === '/dashboard'
    }
    return $page.url.pathname.startsWith(href)
  }
</script>

<Sidebar>
  <SidebarHeader>
    <div class="flex items-center gap-3 px-2 py-4">
      <div class="w-8 h-8 bg-sidebar-primary rounded-lg flex items-center justify-center">
        <UserCheck class="w-5 h-5 text-sidebar-primary-foreground" />
      </div>
      <div>
        <h1 class="font-semibold text-sidebar-foreground">ProcureServe</h1>
        <p class="text-xs text-sidebar-foreground/70">Candidate Portal</p>
      </div>
    </div>
  </SidebarHeader>

  <SidebarContent>
    {#each navigationItems as section}
      <SidebarGroup>
        <SidebarGroupLabel>{section.section}</SidebarGroupLabel>
        <SidebarGroupContent>
          <SidebarMenu>
            {#each section.items as item}
              <SidebarMenuItem>
                <SidebarMenuButton 
                  isActive={isActive(item.href)}
                  on:click={() => navigateTo(item.href)}
                >
                  <svelte:component this={item.icon} class="w-4 h-4" />
                  <span>{item.name}</span>
                </SidebarMenuButton>
              </SidebarMenuItem>
            {/each}
          </SidebarMenu>
        </SidebarGroupContent>
      </SidebarGroup>
    {/each}
  </SidebarContent>

  <SidebarFooter>
    <SidebarMenu>
      <SidebarMenuItem>
        <SidebarMenuButton on:click={signOut}>
          <LogOut class="w-4 h-4" />
          <span>Sign Out</span>
        </SidebarMenuButton>
      </SidebarMenuItem>
    </SidebarMenu>
    
    {#if candidateProfile}
      <div class="px-2 py-2 border-t border-sidebar-border">
        <div class="flex items-center gap-2">
          <div class="w-8 h-8 bg-sidebar-accent rounded-full flex items-center justify-center">
            <User class="w-4 h-4 text-sidebar-accent-foreground" />
          </div>
          <div class="flex-1 min-w-0">
            <p class="text-sm font-medium text-sidebar-foreground truncate">
              {candidateProfile.first_name} {candidateProfile.last_name}
            </p>
            <p class="text-xs text-sidebar-foreground/70 truncate">
              {candidateProfile.email}
            </p>
          </div>
        </div>
      </div>
    {/if}
  </SidebarFooter>
</Sidebar>
