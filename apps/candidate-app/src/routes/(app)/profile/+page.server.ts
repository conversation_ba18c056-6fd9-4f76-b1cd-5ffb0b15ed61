import { createSupabaseServerClient } from '$lib/supabase'
import { redirect } from '@sveltejs/kit'
import type { PageServerLoad } from './$types'

export const load: PageServerLoad = async ({ cookies }) => {
  const supabase = createSupabaseServerClient(cookies)

  const { data: { session } } = await supabase.auth.getSession()
  
  if (!session?.user) {
    throw redirect(303, '/login')
  }

  // Get candidate profile
  const { data: candidateProfile, error } = await supabase
    .from('candidates')
    .select('*')
    .eq('auth_user_id', session.user.id)
    .single()

  if (error) {
    console.error('Error fetching candidate profile:', error)
  }

  return {
    candidateProfile
  }
}
